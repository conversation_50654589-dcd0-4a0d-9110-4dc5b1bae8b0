import express from 'express';
import { validateDr } from '../middlewares/validationMiddleware';
import { getQuestionnaireConfigs, getQuestionnaireConfig, updateQuestionnaireThreshold, updateQuestionnaireConfig } from '../controllers/admin/questionnaire';

const router = express.Router();
const currentVersion = 'v1.0';

router.use(validateDr);

router.get(`/${currentVersion}/questionnaires`, getQuestionnaireConfigs);
router.get(`/${currentVersion}/questionnaires/:questionnaireId`, getQuestionnaireConfig);
router.put(`/${currentVersion}/questionnaires/:questionnaireId/threshold`, updateQuestionnaireThreshold);
router.put(`/${currentVersion}/questionnaires/:questionnaireId`, updateQuestionnaireConfig);

export default router;
